<?php

namespace App\Modules\Transfer\Services;

use App\Events\DomainHistoryEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Constants\TransferTransactionTypes;
use App\Modules\Transfer\Jobs\CancelDomainTransfer;
use App\Modules\Transfer\Jobs\SendEppTransferRequestResponse;
use App\Modules\Transfer\Jobs\TransferEppDomain;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class TransferDomainService
{
    use UserLoggerTrait;

    public static function instance()
    {
        $transferDomainService = new self;

        return $transferDomainService;
    }

    public function store(int $registeredDomainId): void
    {
        $now = Carbon::now();

        $insertData = [
            'registered_domain_id' => $registeredDomainId,
            'status' => TransferRequest::PENDING,
            'created_at' => $now,
            'updated_at' => $now,
        ];

        DB::table('transfer_domains')->insert($insertData);
    }

    public function sendResponse(array $transferDomainIds, string $action): string
    {
        $data = TransferDataQueryService::instance()->getTransferUpdateData($transferDomainIds);
        $this->softDelete($transferDomainIds);

        foreach ($data as $domain) {
            $domainName = $domain->domain_name;
            $domainId = $domain->domain_id;
            $regDomainId = $domain->registered_domain_id;
            $transferId = $domain->id;

            SendEppTransferRequestResponse::dispatch($transferId, $domainName, $domainId, $regDomainId, auth()->user()->id, auth()->user()->email, $action);

            if ($action === TransferTransactionTypes::APPROVE) {
                event(new DomainHistoryEvent(['domain_id' => $domainId, 'type' => 'TRANSFER_OUTBOUND_APPROVED', 'user_id' => auth()->user()->id ?? null, 'status' => 'success', 'message' => 'Domain "'.$domainName.'" outbound transfer approved from Server. Transfer completed successfully.', 'payload' => $domain]));
            } elseif ($action === TransferTransactionTypes::REJECT) {
                event(new DomainHistoryEvent(['domain_id' => $domainId, 'type' => 'TRANSFER_OUTBOUND_REJECTED', 'user_id' => auth()->user()->id ?? null, 'status' => 'success', 'message' => 'Domain "'.$domainName.'" outbound transfer rejected from Server. Transfer failed.', 'payload' => $domain]));
            } else {
                app(AuthLogger::class)->error('Event Unknown transfer action: '.$action.' for domain ID: '.$domainId);
            }
        }

        return $this->message($action);
    }

    public function update(string $domain, $registered_domain_id, $fields, string $email): void
    {
        $fields['updated_at'] = Carbon::now();

        DB::table('transfer_domains')->where('registered_domain_id', $registered_domain_id)
            ->orderBy('created_at', 'desc')->limit(1)
            ->update($fields);

        if (isset($fields['status'])) {
            app(AuthLogger::class)->info($this->fromWho('Updated transfer domain status of '.$domain.' to '.$fields['status'], $email));
        }
    }

    // PRIVATE Function

    private function message(string $action): string
    {
        $message = '';

        switch ($action) {
            case TransferTransactionTypes::APPROVE:
                $message = 'The transfer approval is currently being processed. Please await further notifications.';
                break;
            case TransferTransactionTypes::REJECT:
                $message = 'The transfer rejection is currently being processed. Please await further notifications.';
                break;
        }

        return $message;
    }

    private function softDelete(array $transfer_domain_ids): void
    {
        $now = Carbon::now();

        DB::table('transfer_domains')->whereIn('id', $transfer_domain_ids)
            ->update(['updated_at' => $now, 'deleted_at' => $now]);
    }
}
