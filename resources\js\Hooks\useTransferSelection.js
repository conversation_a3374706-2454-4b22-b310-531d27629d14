import { useState, useCallback } from 'react';

/**
 * Custom hook for managing transfer selection state
 * Provides reusable functionality for selecting/deselecting transfers
 */
export function useTransferSelection(transfers = []) {
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectAll, setSelectAll] = useState(false);

    /**
     * <PERSON>le select all checkbox change
     * @param {boolean} checked - Whether select all is checked
     */
    const handleSelectAllChange = useCallback((checked) => {
        setSelectAll(checked);
        
        if (checked) {
            const allIds = transfers.map((item) => item.id);
            setSelectedItems(allIds);
        } else {
            setSelectedItems([]);
        }
    }, [transfers]);

    /**
     * Handle individual item checkbox change
     * @param {string|number} itemId - ID of the item
     * @param {boolean} checked - Whether the item is checked
     */
    const handleItemCheckboxChange = useCallback((itemId, checked) => {
        setSelectedItems((prev) => {
            let updated;
            if (checked) {
                updated = [...prev, itemId];
            } else {
                updated = prev.filter((id) => id !== itemId);
            }

            // Update select all state based on whether all items are selected
            setSelectAll(updated.length === transfers.length && transfers.length > 0);
            
            return updated;
        });
    }, [transfers.length]);

    /**
     * Clear all selections
     */
    const clearSelection = useCallback(() => {
        setSelectedItems([]);
        setSelectAll(false);
    }, []);

    /**
     * Check if an item is selected
     * @param {string|number} itemId - ID of the item
     * @returns {boolean} Whether the item is selected
     */
    const isItemSelected = useCallback((itemId) => {
        return selectedItems.includes(itemId);
    }, [selectedItems]);

    /**
     * Get count of selected items
     * @returns {number} Number of selected items
     */
    const getSelectedCount = useCallback(() => {
        return selectedItems.length;
    }, [selectedItems.length]);

    /**
     * Check if any items are selected
     * @returns {boolean} Whether any items are selected
     */
    const hasSelection = useCallback(() => {
        return selectedItems.length > 0;
    }, [selectedItems.length]);

    /**
     * Select specific items by IDs
     * @param {Array} ids - Array of IDs to select
     */
    const selectItems = useCallback((ids) => {
        setSelectedItems(ids);
        setSelectAll(ids.length === transfers.length && transfers.length > 0);
    }, [transfers.length]);

    /**
     * Toggle selection for a specific item
     * @param {string|number} itemId - ID of the item to toggle
     */
    const toggleItemSelection = useCallback((itemId) => {
        const isSelected = selectedItems.includes(itemId);
        handleItemCheckboxChange(itemId, !isSelected);
    }, [selectedItems, handleItemCheckboxChange]);

    return {
        selectedItems,
        selectAll,
        handleSelectAllChange,
        handleItemCheckboxChange,
        clearSelection,
        isItemSelected,
        getSelectedCount,
        hasSelection,
        selectItems,
        toggleItemSelection,
    };
}
