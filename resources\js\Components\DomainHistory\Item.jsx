import React from "react";
import { Link, router } from "@inertiajs/react";
import Checkbox from "@/Components/Checkbox";
import Status from "./DomainStatusIndicators";
import { MdMoreVert } from "react-icons/md";
import DropDownContainer from "../DropDownContainer";
import { useState, useRef } from "react";
import useOutsideClick from "@/Util/useOutsideClick";
import CreateDomainDeletionModal from "../RequestDelete/CreateDomainDeletionModal";
import { CiCircleCheck, CiCircleRemove } from "react-icons/ci";

function Item({ domain, transactionTypes, isSelected, onCheckboxChange }) {
    const handleCheckboxChange = (e) => {
        const isChecked = e.target.checked;
        onCheckboxChange(domain.id, domain.is_active, isChecked);
    };
    const [confirmingUserDeletion, setConfirmingUserDeletion] = useState(false);
    const closeModal = () => {
        setConfirmingUserDeletion(false);
    };
    const ref = useRef();
    const [show, setShow] = useState(false);

    useOutsideClick(ref, () => {
        setShow(false);
    });

    function handleOnSubmit() {
        setConfirmingUserDeletion(true);
    }

    const status = domain.status === "ACTIVE";

    console.log(domain);

    return (
        <tr className="border-t mt-2">
            <td className="py-2 px-4 pt-4 pb-4">
                <label className="flex items-center space-x-2">
                    <div
                        style={{
                            visibility:
                                domain.status === "ACTIVE"
                                    ? "visible"
                                    : "hidden",
                        }}
                    >
                        <Checkbox
                            name="domain"
                            value={domain.id}
                            checked={isSelected}
                            handleChange={handleCheckboxChange}
                        />
                    </div>
                    <span>{domain.name}</span>
                </label>
            </td>
            <td className="py-2 px-4">
                {transactionTypes[domain.type] || "Unknown Type"}
            </td>
            <td className="py-2 px-4">{domain.domain_email}</td>
            <td className="py-2 px-4">
                {domain.created_at
                    ? new Date(
                          Date.parse(domain.created_at)
                      ).toLocaleDateString()
                    : "Invalid Date"}
            </td>
            <td className="py-2 px-4">
                {status ? (
                    <CiCircleCheck className="text-green-400 text-md" />
                ) : (
                    <CiCircleRemove className="text-red-500 text-md" />
                )}
            </td>
            <td className="py-2 px-4">
                <Status domain={domain} locked_until={domain.locked_until} />
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>

                    <DropDownContainer show={show}>
                        <button
                            onClick={() =>
                                router.visit(
                                    route("domain.history.show", {
                                        id: domain.id,
                                    })
                                )
                            }
                            className="px-5 py-1 justify-start flex text-left hover:bg-gray-100 w-full"
                        >
                            View Log
                        </button>
                        {domain.status === "ACTIVE" && (
                            <button
                                className="px-5 py-1 justify-start flex"
                                onClick={handleOnSubmit}
                            >
                                Delete
                            </button>
                        )}
                    </DropDownContainer>
                </span>
            </td>
            <td className="py-2 px-4">
                <CreateDomainDeletionModal
                    isOpen={confirmingUserDeletion}
                    onClose={closeModal}
                    domain={domain}
                />
            </td>
        </tr>
    );
}

export default Item;
