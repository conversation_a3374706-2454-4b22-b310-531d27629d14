import AdminLayout from "@/Layouts/AdminLayout";
import React, { useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Item from "@/Components/Transfer/Item";
import Filter from "@/Components/Transfer/Filter";
import {
    MdFilterList,
    MdOutlineSettings,
    MdOutlineSwapVert,
} from "react-icons/md";
import SecondaryButton from "@/Components/SecondaryButton";
import SearchInput from "@/Components/Util/SearchInput";
import Checkbox from "@/Components/Checkbox";
import { getEventValue } from "@/Util/TargetInputEvent";
import { router } from "@inertiajs/react";
import SearchNoDomainFound from "@/Components/Domain/Search/SearchNoDomainFound";
import { useTransferActions } from "@/Hooks/useTransferActions";
import { useTransferSelection } from "@/Hooks/useTransferSelection";

export default function Index({ items }) {
    const [transfers] = useState(items);
    const query = route().params;
    const [limit, setLimit] = useState(parseInt(query.limit) || 20);

    // Use custom hooks for transfer operations
    const {
        isProcessing,
        approveTransfer,
        rejectTransfer,
        approveBulkTransfers,
        rejectBulkTransfers
    } = useTransferActions();

    const {
        selectedItems,
        selectAll,
        handleSelectAllChange,
        handleItemCheckboxChange,
        clearSelection,
        isItemSelected,
        hasSelection
    } = useTransferSelection(transfers);

    const handleApproveTransfer = (transfer) => {
        approveTransfer(transfer, () => {
            // Refresh page or update state as needed
            router.reload({ only: ['items'] });
        });
    };

    const handleRejectTransfer = (transfer) => {
        rejectTransfer(transfer, () => {
            // Refresh page or update state as needed
            router.reload({ only: ['items'] });
        });
    };

    const handleBulkApprove = () => {
        if (!hasSelection()) return;

        approveBulkTransfers(selectedItems, () => {
            clearSelection();
            router.reload({ only: ['items'] });
        });
    };

    const handleBulkReject = () => {
        if (!hasSelection()) return;

        rejectBulkTransfers(selectedItems, () => {
            clearSelection();
            router.reload({ only: ['items'] });
        });
    };

    const handleSelectAllChangeWrapper = (e) => {
        const checked = getEventValue(e);
        handleSelectAllChange(checked);
    };

    console.log(items);

    const handleLimitChange = (e) => {
        const newLimit = parseInt(getEventValue(e));
        setLimit(newLimit);
        router.get(
            route("domain.transfer.view"),
            { ...route().params, limit: newLimit, page: 1 },
            {
                preserveScroll: true,
                preserveState: true,
            }
        );
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div>
                    <h1 className="text-4xl font-bold pl-3">Domain Transfer</h1>
                    <p className="text-gray-600 pl-3">
                        List of domains that have been transferred.
                    </p>
                </div>

                <div className={`flex items-center space-x-4 justify-end`}>
                    <SecondaryButton
                        onClick={handleBulkApprove}
                        processing={selectedItems.length === 0}
                    >
                        APPROVE SELECTED
                    </SecondaryButton>
                </div>
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px", left: "15px" }}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="flex justify-between items-center mt-4 pt-4 pb-4 pl-3">
                    <div className="flex items-center space-x-2">
                        <MdFilterList className="text-xl" />
                        <span>Filter:</span>
                        <Filter />
                    </div>

                    <SearchInput placeholder="Search domain" />
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <label className="flex items-center pl-2 space-x-2">
                                        <Checkbox
                                            name="select_all"
                                            value="select_all"
                                            checked={selectAll}
                                            handleChange={handleSelectAllChange}
                                        />
                                        <span className="">Domain</span>
                                        <button
                                            disabled={transfers.length === 0}
                                        >
                                            <MdOutlineSwapVert />
                                        </button>
                                    </label>
                                </th>
                                <th>
                                    <span>User</span>
                                </th>
                                <th>
                                    <span>Date Created</span>
                                </th>
                                <th>
                                    <span>Status</span>
                                </th>
                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {transfers.length > 0 ? (
                                transfers.map((transfer) => (
                                    <Item
                                        key={transfer.id}
                                        item={transfer}
                                        isSelected={selectedItems.includes(
                                            transfer.id
                                        )}
                                        onCheckboxChange={
                                            handleItemCheckboxChange
                                        }
                                        onApproveTransfer={
                                            handleApproveTransfer
                                        }
                                    />
                                ))
                            ) : (
                                <tr>
                                    <td colSpan={6}>
                                        <SearchNoDomainFound />
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </AdminLayout>
    );
}
