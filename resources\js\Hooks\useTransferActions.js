import { useState } from 'react';
import { router } from '@inertiajs/react';
import { toast } from 'react-toastify';

/**
 * Custom hook for handling transfer actions (approve, reject)
 * Provides reusable functionality for transfer operations
 */
export function useTransferActions() {
    const [isProcessing, setIsProcessing] = useState(false);

    /**
     * Handle single transfer action
     * @param {Object} transfer - Transfer object
     * @param {string} action - Action to perform ('approve' or 'reject')
     * @param {Function} onSuccess - Success callback
     * @param {Function} onError - Error callback
     */
    const handleSingleTransferAction = (transfer, action, onSuccess, onError) => {
        if (isProcessing) return;

        setIsProcessing(true);

        router.post(
            route("transfer.outbound.response", { action, ids: transfer.id }),
            {},
            {
                onSuccess: () => {
                    toast.success(`Transfer ${action}d for ${transfer.domain}`);
                    setIsProcessing(false);
                    if (onSuccess) onSuccess(transfer, action);
                },
                onError: (errors) => {
                    toast.error(`Failed to ${action} transfer`);
                    console.error(errors);
                    setIsProcessing(false);
                    if (onError) onError(errors, transfer, action);
                },
            }
        );
    };

    /**
     * Handle bulk transfer actions
     * @param {Array} selectedIds - Array of selected transfer IDs
     * @param {string} action - Action to perform ('approve' or 'reject')
     * @param {Function} onSuccess - Success callback
     * @param {Function} onError - Error callback
     */
    const handleBulkTransferAction = (selectedIds, action, onSuccess, onError) => {
        if (isProcessing || selectedIds.length === 0) return;

        setIsProcessing(true);
        const ids = selectedIds.join(",");

        router.post(
            route("transfer.outbound.response", { action, ids }),
            {},
            {
                onSuccess: () => {
                    toast.success(`${selectedIds.length} transfers ${action}d successfully`);
                    setIsProcessing(false);
                    if (onSuccess) onSuccess(selectedIds, action);
                },
                onError: (errors) => {
                    toast.error(`Failed to ${action} selected transfers`);
                    console.error(errors);
                    setIsProcessing(false);
                    if (onError) onError(errors, selectedIds, action);
                },
            }
        );
    };

    /**
     * Approve single transfer
     */
    const approveTransfer = (transfer, onSuccess, onError) => {
        handleSingleTransferAction(transfer, 'approve', onSuccess, onError);
    };

    /**
     * Reject single transfer
     */
    const rejectTransfer = (transfer, onSuccess, onError) => {
        handleSingleTransferAction(transfer, 'reject', onSuccess, onError);
    };

    /**
     * Approve multiple transfers
     */
    const approveBulkTransfers = (selectedIds, onSuccess, onError) => {
        handleBulkTransferAction(selectedIds, 'approve', onSuccess, onError);
    };

    /**
     * Reject multiple transfers
     */
    const rejectBulkTransfers = (selectedIds, onSuccess, onError) => {
        handleBulkTransferAction(selectedIds, 'reject', onSuccess, onError);
    };

    return {
        isProcessing,
        approveTransfer,
        rejectTransfer,
        approveBulkTransfers,
        rejectBulkTransfers,
        handleSingleTransferAction,
        handleBulkTransferAction,
    };
}
