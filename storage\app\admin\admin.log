[2025-09-18 06:01:43] local.INFO: Domain History: Domain "hyunta.net" outbound transfer approved from Server. Transfer completed successfully.  
[2025-09-18 06:03:06] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:03:06] local.ERROR: {"query":[],"parameter":[],"error":"TypeError","message":"App\\Modules\\Transfer\\Services\\EppTransferService::sendHttpPostRequest(): Argument #2 ($path) must be of type string, null given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Modules\\Transfer\\Services\\EppTransferService.php on line 80","code":0}  
[2025-09-18 06:13:41] local.ERROR: Event Unknown transfer action: approve for domain ID: 159  
[2025-09-18 06:13:42] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route transfer\/outbound\/approve\/3. Supported methods: POST.","code":0}  
[2025-09-18 06:14:31] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:14:31] local.ERROR: a@a.a Method Illuminate\Http\Client\PendingRequest::transfer does not exist.  
[2025-09-18 06:14:31] local.ERROR: a@a.a Transfer job failed: Error Unknown  
[2025-09-18 06:14:31] local.INFO: Transfer job attempt: 2/3  
[2025-09-18 06:14:31] local.ERROR: {"query":[],"parameter":[],"error":"App\\Exceptions\\FailedRequestException","message":"Error Unknown","code":520}  
[2025-09-18 06:14:31] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:14:31] local.ERROR: a@a.a Method Illuminate\Http\Client\PendingRequest::transfer does not exist.  
[2025-09-18 06:14:31] local.ERROR: a@a.a Transfer job failed: Error Unknown  
[2025-09-18 06:14:31] local.INFO: Transfer job attempt: 1/3  
[2025-09-18 06:14:31] local.ERROR: {"query":[],"parameter":[],"error":"App\\Exceptions\\FailedRequestException","message":"Error Unknown","code":520}  
[2025-09-18 06:19:33] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:19:36] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:19:37] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:19:37.743+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:19:37.743+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:19:37] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:19:37] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:19:37] local.INFO: Transfer job attempt: 3/3  
[2025-09-18 06:19:37] local.ERROR: a@a.a Transfer job permanently failed after 3 attempts: Retry  
[2025-09-18 06:19:37] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:19:37] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:19:37] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:19:38] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:19:38.963+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:19:38.963+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:19:38] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:19:38] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:19:38] local.INFO: Transfer job attempt: 2/3  
[2025-09-18 06:19:38] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:23:41] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:23:42] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:23:42] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:23:43.684+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:23:43.684+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:23:42] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:23:42] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:23:42] local.INFO: Transfer job attempt: 3/3  
[2025-09-18 06:23:42] local.ERROR: a@a.a Transfer job permanently failed after 3 attempts: Retry  
[2025-09-18 06:23:42] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:24:57] local.INFO: Domain History: Domain "hyunta.com" outbound transfer approved from Server. Transfer completed successfully.  
[2025-09-18 06:24:57] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route transfer\/outbound\/approve\/3. Supported methods: POST.","code":0}  
[2025-09-18 06:25:07] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:25:08] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:25:08] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:25:09.542+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:25:09.542+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:25:08] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:25:08] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:25:08] local.INFO: Transfer job attempt: 1/3  
[2025-09-18 06:25:08] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:27:30] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Class App\\Modules\\Epp\\Services\\LocalEppUrl contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (App\\Modules\\Epp\\Services\\EppUrlInterface::transfer)","code":0}  
[2025-09-18 06:28:47] local.INFO: Domain History: Domain "hyunta.net" outbound transfer approved from Server. Transfer completed successfully.  
[2025-09-18 06:28:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route transfer\/outbound\/approve\/2. Supported methods: POST.","code":0}  
[2025-09-18 06:29:01] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:29:01] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:29:02] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:29:02.841+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:29:02.841+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:29:02] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:29:02] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:29:02] local.INFO: Transfer job attempt: 2/3  
[2025-09-18 06:29:02] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:29:02] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:29:02] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:29:02] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:29:03.753+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:29:03.753+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:29:02] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:29:02] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:29:02] local.INFO: Transfer job attempt: 1/3  
[2025-09-18 06:29:02] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
