<?php

use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Constants\TransferTransactionTypes;

$base = '/domain/transfer';

return [
  'dev_base_pir' => '/pir/v1-dev',
  'prod_base_pir' => '/pir/v1',
  'dev_base_verisign' => '/verisign/v1-dev',
  'prod_base_verisign' => '/verisign/v1',
  'v3_single_pir' => '/pir/v3',
  'v3_single_verisign' => '/verisign/v3',
  'v3_multiple_pir' => '/pir/v3_1',
  'v3_multiple_verisign' => '/verisign/v3_1',
  'info' => '/domain/info',

  TransferTransactionTypes::REJECT => $base . '/reject',
  TransferTransactionTypes::QUERY => $base . '/query',
  TransferRequest::OUTBOUND . "." . EppDomainStatus::TRANSFER_SERVER_APPROVED => $base . '/outbound-server-approved',

  'response' => [
    'ok' => 'OK',
    'error' => 'error',
    'disabled' => 'SERVICE_UNAVAILABLE',
  ],
];